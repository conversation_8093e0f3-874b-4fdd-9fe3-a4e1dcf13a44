{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue", "mtime": 1754019577741}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAqHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/comindexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-04 09:23:59\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-05-07 11:05:02\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\index.vue\r\n-->\r\n<template>\r\n  <div class=\"contents\">\r\n    <div class=\"contetn_left\">\r\n      <div class=\"pagetab\">\r\n        <!-- <div class=\"item\">实时监测</div> -->\r\n        \r\n      </div>\r\n      <ItemWrap class=\"contetn_left-top contetn_lr-item\" title=\"基本信息\">\r\n        \r\n        <div style=\"height: 50px;padding-top: 10px;padding-left: 30px;font-size: 19px;\" >船名：{{ shipName }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">SN：{{ shipSn }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">经度：{{ shipLongitude + shipLongitudeH }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">纬度：{{ shipLatitude + shipLatitudeH }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">UTC：{{ shipUtc }}</div>\r\n\r\n      </ItemWrap>\r\n      <ItemWrap class=\"contetn_left-center contetn_lr-item\" title=\"姿态信息\">\r\n      \r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          船首向\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeHeading }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          横摇\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeRolling }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          纵摇\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{attitudePitch}}</div>\r\n        </div>\r\n        \r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          高度\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{attitudeHeight}}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          经度\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{attitudeLongitude}}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          纬度\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeLatitude }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          距离\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeDistance }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          船速\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeSpeed }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 10px;font-size: 14px;\">\r\n          更新时间\r\n          <div style=\"padding-top: 5px;font-size: 16px;\">{{ attitudeUptime }}</div>\r\n        </div>\r\n\r\n      </ItemWrap>\r\n      <ItemWrap class=\"contetn_left-bottom contetn_lr-item\" title=\"气象信息\">\r\n        <div id=\"main1\" style=\"float: left;width:180px;text-align: center;height: 200px;padding-top: 13px;padding-left: -30px;float: left;\">\r\n          \r\n        </div>\r\n        <div id=\"main2\" style=\"float: left;width:180px;text-align: center;height: 200px;padding-top: 13px;padding-left: 15px;float: left;\">\r\n          \r\n        </div>\r\n          <div style=\"padding-top: 5px;font-size: 16px;\">{{ awsUptime }}</div>\r\n      </ItemWrap>\r\n    </div>\r\n    <div class=\"contetn_center\">\r\n      <CenterMap class=\"contetn_center_top\" />\r\n      <!--\r\n      <ItemWrap class=\"contetn_center-bottom\" title=\"安装计划\">\r\n        <CenterBottom />\r\n      </ItemWrap>\r\n      -->\r\n    </div>\r\n    <!--\r\n    <div class=\"contetn_right\">\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"报警次数\"\r\n      >\r\n        <RightTop />\r\n      </ItemWrap>\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"报警排名(TOP8)\"\r\n        style=\"padding: 0 10px 16px 10px\"\r\n      >\r\n        <RightCenter />\r\n      </ItemWrap>\r\n      \r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"数据统计图 \"\r\n      >\r\n        <RightBottom />\r\n      </ItemWrap>\r\n    </div>\r\n    -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport LeftTop from './left-top.vue'\r\nimport LeftCenter from \"./left-center.vue\";\r\nimport LeftBottom from \"./left-bottom.vue\";\r\nimport CenterMap from \"./center-map.vue\";\r\nimport CenterBottom from \"./center-bottom.vue\";\r\nimport RightTop from \"./right-top.vue\";\r\nimport RightCenter from \"./right-center.vue\";\r\nimport RightBottom from \"./right-bottom.vue\";\r\nimport { initWebSocket, dataModule, manualClose, clearData } from '@/utils/webSocket'\r\n\r\nexport default {\r\n  components: {\r\n    LeftTop,\r\n    LeftCenter,\r\n    LeftBottom,\r\n    CenterMap,\r\n    RightTop,\r\n    RightCenter,\r\n    RightBottom,\r\n    CenterBottom,\r\n  },\r\n  data() {\r\n    return {\r\n      shipName: \"\",\r\n      shipMmsi: \"\",\r\n      shipCallSign: \"\",\r\n      shipSn: \"\",\r\n      shipLongitude: \"\",\r\n      shipLatitude: \"\",\r\n      shipLongitudeH: \"\",\r\n      shipLatitudeH: \"\",\r\n      shipUtc: \"\",\r\n      attitudeHeading: \"\",\r\n      attitudeRolling: \"\",\r\n      attitudePitch: \"\",\r\n      attitudeHeight: \"\",\r\n      attitudeLongitude: \"\",\r\n      attitudeLatitude: \"\",\r\n      attitudeDistance: \"\",\r\n      attitudeSpeed: \"\",\r\n      attitudeUptime: \"\",\r\n      awsUptime: \"\",\r\n      awsMyChart1: null,\r\n      awsMyChart2: null\r\n    };\r\n  },\r\n  filters: {\r\n    numsFilter(msg) {\r\n      return msg || 0;\r\n    },\r\n  },\r\n  created() {\r\n    this.startDataMonitoring();\r\n  },\r\n\r\n  mounted() {\r\n    //销毁其他页面连接\r\n    manualClose();\r\n\r\n    // 清空数据\r\n    clearData();\r\n\r\n    //与后端建立长连接\r\n    // 检查并恢复企业状态\r\n    this.restoreCompanyState();\r\n    initWebSocket();\r\n\r\n    var chartDom1 = document.getElementById('main1');\r\n    var myChart1 = echarts.init(chartDom1, 'dark');\r\n    var option1;\r\n    var chartDom2 = document.getElementById('main2');\r\n    var myChart2 = echarts.init(chartDom2, 'dark');\r\n    var option2;\r\n    // 气象仪图表数据\r\n    option1 = {\r\n      backgroundColor: \"#03050c\",\r\n      series: [\r\n        {\r\n          min: 0,\r\n          max: 360,\r\n          splitNumber: 4,\r\n          radius: \"100%\",\r\n          //startAngle: 90,\r\n          //endAngle: 90.0000001,\r\n          name: 'Pressure',\r\n          type: 'gauge',\r\n          progress: {\r\n            show: true\r\n          },\r\n          detail: {\r\n            valueAnimation: true,\r\n            formatter: '{value}',\r\n            offsetCenter: [0, '80%']\r\n          },\r\n          data: [\r\n            {\r\n              value: 0,\r\n              name: '风向'\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n\r\n    option2 = {\r\n      backgroundColor: \"#03050c\",\r\n      series: [\r\n        {\r\n          min: 0,\r\n          max: 360,\r\n          splitNumber: 4,\r\n          radius: \"100%\",\r\n          //startAngle: 90,\r\n          //endAngle: 90.0000001,\r\n          name: 'Pressure',\r\n          type: 'gauge',\r\n          progress: {\r\n            show: true\r\n          },\r\n          detail: {\r\n            valueAnimation: true,\r\n            formatter: '{value}',\r\n            offsetCenter: [0, '80%']\r\n          },\r\n          data: [\r\n            {\r\n              value: 0,\r\n              name: '风速'\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n    option1 && myChart1.setOption(option1);\r\n    option2 && myChart2.setOption(option2);\r\n    this.awsMyChart1 = myChart1;\r\n    this.awsMyChart2 = myChart2;\r\n\r\n  },\r\n  methods: {\r\n\r\n    // 恢复企业状态\r\n    restoreCompanyState() {\r\n      let deptId = '101'; // 默认值\r\n      try {\r\n        const sessionCompany = sessionStorage.getItem('selectedCompany');\r\n        if (sessionCompany) {\r\n          const companyInfo = JSON.parse(sessionCompany);\r\n          if (companyInfo.sendtext) {\r\n            dataModule.sendtext = companyInfo.sendtext;\r\n            return;\r\n          }\r\n          if (companyInfo.deptId) {\r\n            deptId = companyInfo.deptId;\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.warn('读取sessionStorage中的企业信息失败:', error);\r\n      }\r\n\r\n      dataModule.sendtext = `type66#${deptId}#0#0B01,0B02,0B03,0B04,0B05,0B06`;\r\n    },\r\n\r\n    startDataMonitoring() {\r\n      this.wsCheckTimer = setInterval(() => {\r\n        this.checkWebSocketData()\r\n      }, 1000)\r\n    },\r\n    // 检查WebSocket数据\r\n    checkWebSocketData() {\r\n      if (dataModule.D0B01) {\r\n        this.shipName = dataModule.D0B01.name;\r\n        this.shipMmsi = dataModule.D0B01.mmsi;\r\n        this.shipCallSign = dataModule.D0B01.callSign;\r\n        this.shipSn = dataModule.D0B01.sn;\r\n        this.shipLongitude = dataModule.D0B01.longitude;\r\n        this.shipLatitude = dataModule.D0B01.latitude;\r\n        this.shipLongitudeH = dataModule.D0B01.longitudeHemisphere;\r\n        this.shipLatitudeH = dataModule.D0B01.latitudeHemisphere;\r\n        this.shipUtc = dataModule.D0B01.utc;\r\n\r\n        this.attitudeHeading = dataModule.D0B02.attitudeHeading;\r\n        this.attitudeRolling = dataModule.D0B02.attitudeRolling;\r\n        this.attitudePitch = dataModule.D0B02.attitudePitch;\r\n        this.attitudeHeight = dataModule.D0B02.attitudeHeight;\r\n        this.attitudeLongitude = dataModule.D0B02.attitudeLongitude;\r\n        this.attitudeLatitude = dataModule.D0B02.attitudeLatitude;\r\n        this.attitudeDistance = dataModule.D0B02.attitudeDistance;\r\n        this.attitudeSpeed = dataModule.D0B02.attitudeSpeed;\r\n        this.attitudeUptime = dataModule.D0B02.attitudeUptime;\r\n        \r\n        //this.awsSpeed = dataModule.D0B03.awsSpeed;\r\n        //this.awsDirection = dataModule.D0B03.awsDirection;\r\n        if(isNaN(dataModule.D0B03.awsSpeed)){\r\n          dataModule.D0B03.awsSpeed = 0;\r\n        }\r\n        if(isNaN(dataModule.D0B03.awsDirection)){\r\n          dataModule.D0B03.awsDirection = 0;\r\n        }\r\n\r\n        this.awsMyChart1.setOption({\r\n          series: [\r\n            {\r\n              data: [\r\n                {\r\n                  value: dataModule.D0B03.awsSpeed\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n\r\n        this.awsMyChart2.setOption({\r\n          series: [\r\n            {\r\n              data: [\r\n                {\r\n                  value: dataModule.D0B03.awsDirection\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n\r\n      }\r\n\r\n      if (dataModule.D0B03) {\r\n        this.awsUptime = dataModule.D0B03.awsUptime;\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n// 内容\r\n.contents {\r\n  .contetn_left,\r\n  .contetn_right {\r\n    width: 430px;\r\n    box-sizing: border-box;\r\n  }\r\n\r\n  .contetn_left {\r\n    height: 960px;\r\n    gap: 10px;\r\n  }\r\n\r\n  .contetn_center {\r\n    height: 960px;\r\n    width: 1439px;\r\n  }\r\n\r\n  //左右两侧 三个块\r\n  .contetn_lr-item {\r\n    height: 311px;\r\n  }\r\n\r\n  .contetn_center_top {\r\n    width: 100%;\r\n  }\r\n\r\n  // 中间\r\n  .contetn_center {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n  }\r\n\r\n  .contetn_center-bottom {\r\n    height: 315px;\r\n  }\r\n\r\n  //左边 右边 结构一样\r\n  .contetn_left,\r\n  .contetn_right {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n    position: relative;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}