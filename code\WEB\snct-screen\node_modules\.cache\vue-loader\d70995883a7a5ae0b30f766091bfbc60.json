{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--5!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue?vue&type=template&id=23b0f17b&scoped=true&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue", "mtime": 1754020417952}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753075298398}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}