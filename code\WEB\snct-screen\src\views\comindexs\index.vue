<!--
 * @Author: daidai
 * @Date: 2022-03-04 09:23:59
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-05-07 11:05:02
 * @FilePath: \web-pc\src\pages\big-screen\view\indexs\index.vue
-->
<template>
  <div class="contents">
    <div class="contetn_left">
      <div class="pagetab">
        <!-- <div class="item">实时监测</div> -->
        
      </div>
      <ItemWrap class="contetn_left-top contetn_lr-item" title="基本信息">
        
        <div style="height: 50px;padding-top: 10px;padding-left: 30px;font-size: 19px;" >船名：{{ shipName }}</div>
        <div style="height: 50px;padding-left: 30px;font-size: 19px;">SN：{{ shipSn }}</div>
        <div style="height: 50px;padding-left: 30px;font-size: 19px;">经度：{{ shipLongitude + shipLongitudeH }}</div>
        <div style="height: 50px;padding-left: 30px;font-size: 19px;">纬度：{{ shipLatitude + shipLatitudeH }}</div>
        <div style="height: 50px;padding-left: 30px;font-size: 19px;">UTC：{{ shipUtc }}</div>

      </ItemWrap>
      <ItemWrap class="contetn_left-center contetn_lr-item" title="姿态信息">
      
        <div style="float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;">
          船首向
          <div style="padding-top: 13px;font-size: 20px;">{{ attitudeHeading }}</div>
        </div>

        <div style="float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;">
          横摇
          <div style="padding-top: 13px;font-size: 20px;">{{ attitudeRolling }}</div>
        </div>

        <div style="float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;">
          纵摇
          <div style="padding-top: 13px;font-size: 20px;">{{attitudePitch}}</div>
        </div>
        
        <div style="float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;">
          高度
          <div style="padding-top: 13px;font-size: 20px;">{{attitudeHeight}}</div>
        </div>

        <div style="float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;">
          经度
          <div style="padding-top: 13px;font-size: 20px;">{{attitudeLongitude}}</div>
        </div>

        <div style="float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;">
          纬度
          <div style="padding-top: 13px;font-size: 20px;">{{ attitudeLatitude }}</div>
        </div>

        <div style="float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;">
          距离
          <div style="padding-top: 13px;font-size: 20px;">{{ attitudeDistance }}</div>
        </div>

        <div style="float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;">
          船速
          <div style="padding-top: 13px;font-size: 20px;">{{ attitudeSpeed }}</div>
        </div>

        <div style="float: left;width:125px;text-align: center;height: 63px;padding-top: 10px;font-size: 14px;">
          更新时间
          <div style="padding-top: 5px;font-size: 16px;">{{ attitudeUptime }}</div>
        </div>

      </ItemWrap>
      <ItemWrap class="contetn_left-bottom contetn_lr-item" title="气象信息">
        <div id="main1" style="float: left;width:180px;text-align: center;height: 200px;padding-top: 13px;padding-left: -30px;float: left;">
          
        </div>
        <div id="main2" style="float: left;width:180px;text-align: center;height: 200px;padding-top: 13px;padding-left: 15px;float: left;">
          
        </div>
          <div style="padding-top: 5px;font-size: 16px;">{{ awsUptime }}</div>
      </ItemWrap>
    </div>
    <div class="contetn_center">
      <CenterMap class="contetn_center_top" />
      <!--
      <ItemWrap class="contetn_center-bottom" title="安装计划">
        <CenterBottom />
      </ItemWrap>
      -->
    </div>
    <!--
    <div class="contetn_right">
      <ItemWrap
        class="contetn_left-bottom contetn_lr-item"
        title="报警次数"
      >
        <RightTop />
      </ItemWrap>
      <ItemWrap
        class="contetn_left-bottom contetn_lr-item"
        title="报警排名(TOP8)"
        style="padding: 0 10px 16px 10px"
      >
        <RightCenter />
      </ItemWrap>
      
      <ItemWrap
        class="contetn_left-bottom contetn_lr-item"
        title="数据统计图 "
      >
        <RightBottom />
      </ItemWrap>
    </div>
    -->
  </div>
</template>

<script>
import * as echarts from 'echarts';
import LeftTop from './left-top.vue'
import LeftCenter from "./left-center.vue";
import LeftBottom from "./left-bottom.vue";
import CenterMap from "./center-map.vue";
import CenterBottom from "./center-bottom.vue";
import RightTop from "./right-top.vue";
import RightCenter from "./right-center.vue";
import RightBottom from "./right-bottom.vue";
import { initWebSocket, dataModule, manualClose, clearData } from '@/utils/webSocket'

export default {
  components: {
    LeftTop,
    LeftCenter,
    LeftBottom,
    CenterMap,
    RightTop,
    RightCenter,
    RightBottom,
    CenterBottom,
  },
  data() {
    return {
      shipName: "",
      shipMmsi: "",
      shipCallSign: "",
      shipSn: "",
      shipLongitude: "",
      shipLatitude: "",
      shipLongitudeH: "",
      shipLatitudeH: "",
      shipUtc: "",
      attitudeHeading: "",
      attitudeRolling: "",
      attitudePitch: "",
      attitudeHeight: "",
      attitudeLongitude: "",
      attitudeLatitude: "",
      attitudeDistance: "",
      attitudeSpeed: "",
      attitudeUptime: "",
      awsUptime: "",
      awsMyChart1: null,
      awsMyChart2: null
    };
  },
  filters: {
    numsFilter(msg) {
      return msg || 0;
    },
  },
  created() {
    this.startDataMonitoring();
  },

  mounted() {
    //销毁其他页面连接
    manualClose();

    // 清空数据
    clearData();

    //与后端建立长连接
    // 检查并恢复企业状态
    this.restoreCompanyState();
    initWebSocket();

    var chartDom1 = document.getElementById('main1');
    var myChart1 = echarts.init(chartDom1, 'dark');
    var option1;
    var chartDom2 = document.getElementById('main2');
    var myChart2 = echarts.init(chartDom2, 'dark');
    var option2;
    // 气象仪图表数据
    option1 = {
      backgroundColor: "#03050c",
      series: [
        {
          min: 0,
          max: 360,
          splitNumber: 4,
          radius: "100%",
          //startAngle: 90,
          //endAngle: 90.0000001,
          name: 'Pressure',
          type: 'gauge',
          progress: {
            show: true
          },
          detail: {
            valueAnimation: true,
            formatter: '{value}',
            offsetCenter: [0, '80%']
          },
          data: [
            {
              value: 0,
              name: '风向'
            }
          ]
        }
      ]
    };

    option2 = {
      backgroundColor: "#03050c",
      series: [
        {
          min: 0,
          max: 360,
          splitNumber: 4,
          radius: "100%",
          //startAngle: 90,
          //endAngle: 90.0000001,
          name: 'Pressure',
          type: 'gauge',
          progress: {
            show: true
          },
          detail: {
            valueAnimation: true,
            formatter: '{value}',
            offsetCenter: [0, '80%']
          },
          data: [
            {
              value: 0,
              name: '风速'
            }
          ]
        }
      ]
    };
    option1 && myChart1.setOption(option1);
    option2 && myChart2.setOption(option2);
    this.awsMyChart1 = myChart1;
    this.awsMyChart2 = myChart2;

  },
  methods: {
    // 转换NMEA格式的经纬度为十进制度数
    convertCoordinate(coordinate, type) {
      if (!coordinate) return '';

      // 移除最后的方向字母（E/W/N/S）
      const direction = coordinate.slice(-1);
      const numericPart = coordinate.slice(0, -1);

      let degrees, minutes;

      if (type === 'longitude') {
        // 经度格式：DDDMM.MMMMMM (前3位是度，后面是分)
        degrees = parseInt(numericPart.substring(0, 3));
        minutes = parseFloat(numericPart.substring(3));
      } else {
        // 纬度格式：DDMM.MMMMMM (前2位是度，后面是分)
        degrees = parseInt(numericPart.substring(0, 2));
        minutes = parseFloat(numericPart.substring(2));
      }

      // 转换为十进制度数
      let decimal = degrees + minutes / 60;

      // 根据方向确定正负
      if (direction === 'W' || direction === 'S') {
        decimal = -decimal;
      }

      // 保留6位小数
      return decimal.toFixed(6);
    },

    // 恢复企业状态
    restoreCompanyState() {
      let deptId = '101'; // 默认值
      try {
        const sessionCompany = sessionStorage.getItem('selectedCompany');
        if (sessionCompany) {
          const companyInfo = JSON.parse(sessionCompany);
          if (companyInfo.sendtext) {
            dataModule.sendtext = companyInfo.sendtext;
            return;
          }
          if (companyInfo.deptId) {
            deptId = companyInfo.deptId;
          }
        }
      } catch (error) {
        console.warn('读取sessionStorage中的企业信息失败:', error);
      }

      dataModule.sendtext = `type66#${deptId}#0#0B01,0B02,0B03,0B04,0B05,0B06`;
    },

    startDataMonitoring() {
      this.wsCheckTimer = setInterval(() => {
        this.checkWebSocketData()
      }, 1000)
    },
    // 检查WebSocket数据
    checkWebSocketData() {
      if (dataModule.D0B01) {
        this.shipName = dataModule.D0B01.name;
        this.shipMmsi = dataModule.D0B01.mmsi;
        this.shipCallSign = dataModule.D0B01.callSign;
        this.shipSn = dataModule.D0B01.sn;
        // 转换经纬度格式
        this.shipLongitude = this.convertCoordinate(dataModule.D0B01.longitude, 'longitude');
        this.shipLatitude = this.convertCoordinate(dataModule.D0B01.latitude, 'latitude');
        this.shipLongitudeH = dataModule.D0B01.longitudeHemisphere;
        this.shipLatitudeH = dataModule.D0B01.latitudeHemisphere;
        this.shipUtc = dataModule.D0B01.utc;

        this.attitudeHeading = dataModule.D0B02.attitudeHeading;
        this.attitudeRolling = dataModule.D0B02.attitudeRolling;
        this.attitudePitch = dataModule.D0B02.attitudePitch;
        this.attitudeHeight = dataModule.D0B02.attitudeHeight;
        this.attitudeLongitude = dataModule.D0B02.attitudeLongitude;
        this.attitudeLatitude = dataModule.D0B02.attitudeLatitude;
        this.attitudeDistance = dataModule.D0B02.attitudeDistance;
        this.attitudeSpeed = dataModule.D0B02.attitudeSpeed;
        this.attitudeUptime = dataModule.D0B02.attitudeUptime;
        
        //this.awsSpeed = dataModule.D0B03.awsSpeed;
        //this.awsDirection = dataModule.D0B03.awsDirection;
        if(isNaN(dataModule.D0B03.awsSpeed)){
          dataModule.D0B03.awsSpeed = 0;
        }
        if(isNaN(dataModule.D0B03.awsDirection)){
          dataModule.D0B03.awsDirection = 0;
        }

        this.awsMyChart1.setOption({
          series: [
            {
              data: [
                {
                  value: dataModule.D0B03.awsSpeed
                }
              ]
            }
          ]
        });

        this.awsMyChart2.setOption({
          series: [
            {
              data: [
                {
                  value: dataModule.D0B03.awsDirection
                }
              ]
            }
          ]
        });

      }

      if (dataModule.D0B03) {
        this.awsUptime = dataModule.D0B03.awsUptime;
      }
    }
  },
};
</script>
<style lang="scss" scoped>
// 内容
.contents {
  .contetn_left,
  .contetn_right {
    width: 430px;
    box-sizing: border-box;
  }

  .contetn_left {
    height: 960px;
    gap: 10px;
  }

  .contetn_center {
    height: 960px;
    width: 1439px;
  }

  //左右两侧 三个块
  .contetn_lr-item {
    height: 311px;
  }

  .contetn_center_top {
    width: 100%;
  }

  // 中间
  .contetn_center {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  .contetn_center-bottom {
    height: 315px;
  }

  //左边 右边 结构一样
  .contetn_left,
  .contetn_right {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    position: relative;
  }
}

</style>
